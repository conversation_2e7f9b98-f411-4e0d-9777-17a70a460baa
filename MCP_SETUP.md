# MCP服务配置修复指南

## 当前状态
✅ **已完成的升级:**
- [x] 修复了主目录下的环境变量配置
- [x] 创建了项目特定的 `.mcp.json` 配置文件
- [x] 创建了环境变量模板 `.env.example`
- [x] 创建了配置验证脚本 `validate-mcp.sh`
- [x] 验证了基本工具可用性
- [x] 安装了GitHub和Slack MCP服务器
- [x] 更新了配置文件以支持新服务器
- [x] 更新了环境变量模板

⚠️ **需要手动处理的问题:**
- [ ] 需要配置API密钥（GitHub、Slack、Brave）
- [ ] 根据需要启用/禁用特定服务器

## 快速修复步骤

### 1. 安装额外的MCP服务器（可选）
```bash
# 已安装的服务器：
# - @modelcontextprotocol/server-filesystem
# - @modelcontextprotocol/server-everything
# - @modelcontextprotocol/server-memory
# - @modelcontextprotocol/server-sequential-thinking
# - @modelcontextprotocol/server-github
# - @modelcontextprotocol/server-slack

# 如需安装其他服务器：
npm install -g @modelcontextprotocol/server-puppeteer  # 浏览器自动化（已弃用）
npm install -g @modelcontextprotocol/server-gitlab     # GitLab集成
npm install -g @modelcontextprotocol/server-redis      # Redis数据库
```

### 2. 配置环境变量
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑 .env 文件，填入实际值
nano .env
```

### 3. 验证配置
```bash
./validate-mcp.sh
```

### 4. 重启编辑器
- 关闭并重新打开 VS Code/Cursor
- 确保MCP服务正常加载

## 配置文件说明

### 项目级配置: `.mcp.json`
包含项目特定的MCP服务配置，限制访问项目目录。

### 用户级配置: `~/.mcp.json`
包含全局MCP服务配置，已在环境变量引用中修复。

### 环境变量配置: `.env`
包含敏感信息和API密钥，已创建模板文件。

## 故障排除

### 如果安装超时
- 检查网络连接
- 使用国内镜像: `npm config set registry https://registry.npmmirror.com`
- 或使用代理

### 如果服务启动失败
- 检查Node.js版本: `node --version` (需v18+)
- 检查npm版本: `npm --version` (需v8+)
- 查看详细错误: `./validate-mcp.sh`

### 权限问题
- 确保有全局安装权限: `sudo npm install -g [package]`
- 或使用nvm管理Node.js版本

## 已配置的服务

1. **filesystem** - 文件系统访问（项目目录限制）
2. **everything** - 全功能服务器（包含HTTP请求、搜索等）
3. **memory** - 内存存储和知识图谱
4. **sequential-thinking** - 顺序思考和问题解决
5. **github** - GitHub API集成（需配置GITHUB_TOKEN）
6. **slack** - Slack团队通信集成（需配置SLACK_BOT_TOKEN和SLACK_TEAM_ID）

## 测试命令

```bash
# 测试单个服务
npx @modelcontextprotocol/server-filesystem --help

# 测试项目配置
claude --mcp-config .mcp.json
```