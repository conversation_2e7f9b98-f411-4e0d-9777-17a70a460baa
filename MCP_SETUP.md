# MCP服务配置修复指南

## 当前状态
✅ **已完成的修复:**
- [x] 修复了主目录下的环境变量配置
- [x] 创建了项目特定的 `.mcp.json` 配置文件
- [x] 创建了环境变量模板 `.env.example`
- [x] 创建了配置验证脚本 `validate-mcp.sh`
- [x] 验证了基本工具可用性

⚠️ **需要手动处理的问题:**
- [ ] 部分MCP服务器包未安装（网络问题）
- [ ] 需要配置API密钥（GitHub、Brave）

## 快速修复步骤

### 1. 安装缺失的MCP服务器
```bash
# 如果网络正常，运行以下命令
npm install -g @modelcontextprotocol/server-fetch
npm install -g @modelcontextprotocol/server-sqlite
npm install -g @modelcontextprotocol/server-brave-search
npm install -g @modelcontextprotocol/server-github
```

### 2. 配置环境变量
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑 .env 文件，填入实际值
nano .env
```

### 3. 验证配置
```bash
./validate-mcp.sh
```

### 4. 重启编辑器
- 关闭并重新打开 VS Code/Cursor
- 确保MCP服务正常加载

## 配置文件说明

### 项目级配置: `.mcp.json`
包含项目特定的MCP服务配置，限制访问项目目录。

### 用户级配置: `~/.mcp.json`
包含全局MCP服务配置，已在环境变量引用中修复。

### 环境变量配置: `.env`
包含敏感信息和API密钥，已创建模板文件。

## 故障排除

### 如果安装超时
- 检查网络连接
- 使用国内镜像: `npm config set registry https://registry.npmmirror.com`
- 或使用代理

### 如果服务启动失败
- 检查Node.js版本: `node --version` (需v18+)
- 检查npm版本: `npm --version` (需v8+)
- 查看详细错误: `./validate-mcp.sh`

### 权限问题
- 确保有全局安装权限: `sudo npm install -g [package]`
- 或使用nvm管理Node.js版本

## 已配置的服务

1. **filesystem** - 文件系统访问
2. **fetch** - HTTP请求
3. **memory** - 内存存储
4. **sequential-thinking** - 顺序思考
5. **github** - GitHub集成（需配置）
6. **brave-search** - 搜索（需配置）

## 测试命令

```bash
# 测试单个服务
npx @modelcontextprotocol/server-filesystem --help

# 测试项目配置
claude --mcp-config .mcp.json
```