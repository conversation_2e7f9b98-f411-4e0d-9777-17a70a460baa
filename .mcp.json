{"mcpServers": {"filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/home/<USER>/soft/Cputroller-1.1"], "description": "File system access for Cputroller project"}, "everything": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-everything"], "description": "All-in-one MCP server with fetch, web search, and more"}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "description": "Memory server for project context"}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "description": "Sequential thinking server for complex tasks"}}}