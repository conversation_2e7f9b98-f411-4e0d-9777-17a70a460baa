{"mcpServers": {"filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/home/<USER>/soft/Cputroller-1.1"], "description": "File system access for Cputroller project"}, "everything": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-everything"], "description": "All-in-one MCP server with fetch, web search, and more"}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "description": "Memory server for project context"}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "description": "Sequential thinking server for complex tasks"}, "github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "description": "GitHub API integration for repository management", "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "${GITHUB_TOKEN}"}}, "slack": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-slack"], "description": "Slack integration for team communication", "env": {"SLACK_BOT_TOKEN": "${SLACK_BOT_TOKEN}", "SLACK_TEAM_ID": "${SLACK_TEAM_ID}"}}}}