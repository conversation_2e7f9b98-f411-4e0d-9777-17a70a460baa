{"mcpServers": {"filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/home/<USER>/soft/Cputroller-1.1"], "env": {}}, "everything": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-everything"], "env": {}}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "env": {}}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "env": {}}, "github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "${GITHUB_TOKEN}"}}, "slack": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-slack"], "env": {"SLACK_BOT_TOKEN": "${SLACK_BOT_TOKEN}", "SLACK_TEAM_ID": "${SLACK_TEAM_ID}"}}, "Context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {}}}}