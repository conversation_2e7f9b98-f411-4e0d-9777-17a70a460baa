#MainWindow {
    background-image: url(:/static/bg.jpg);
    background-repeat: no-repeat;
}

QPushButton {
    border: none;
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 5px;
}

QPushButton:hover {
    background-color: rgba(255, 255, 255, 0.8);
}

QGroupBox {
    background-color: rgba(0, 0, 0, 0.2);
    color: white;
    border-radius: 20px;
}

QGroupBox::title {
    margin: 20px;
}

QGroupBox QLabel[accessibleName="itemname"] {
    color: #dddddd;
}

QLabel {
    color: white;
}

QProgressBar {
    border: none;
    border-radius: 5px;
    text-align: right;
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
}

QProgressBar::chunk {
    background-color: rgb(66, 133, 244);
    border-radius: 5px;
}

QSlider::groove:horizontal {
    background: rgba(255, 255, 255, 0.2);
    height: 20px;
    border-radius: 10px;
}

QSlider::handle:horizontal {
    background-color: white;
    height: 20px;
    width: 20px;
    border-radius: 10px;
}
