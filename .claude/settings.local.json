{"permissions": {"allow": ["Bash(npx --version)", "Bash(node:*)", "Bash(npm install:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./validate-mcp.sh:*)", "Bash(npm config:*)", "WebSearch", "Bash(npm search:*)", "Bash(npm view:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(uv:*)", "Bash(/home/<USER>/validate_mcp_services.sh:*)"], "additionalDirectories": ["/home/<USER>"]}, "enabledMcpjsonServers": ["filesystem", "everything", "memory", "sequential-thinking", "github", "slack"]}