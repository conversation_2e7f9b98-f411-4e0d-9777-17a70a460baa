<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>761</width>
    <height>471</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>761</width>
    <height>471</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>761</width>
    <height>471</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Cputroller</string>
  </property>
  <property name="autoFillBackground">
   <bool>false</bool>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <widget class="QWidget" name="centralWidget">
   <widget class="QGroupBox" name="groupBox">
    <property name="geometry">
     <rect>
      <x>60</x>
      <y>60</y>
      <width>341</width>
      <height>331</height>
     </rect>
    </property>
    <property name="title">
     <string>CPU</string>
    </property>
    <widget class="QLabel" name="label_2">
     <property name="geometry">
      <rect>
       <x>30</x>
       <y>70</y>
       <width>61</width>
       <height>23</height>
      </rect>
     </property>
     <property name="accessibleName">
      <string>itemname</string>
     </property>
     <property name="text">
      <string>温度：</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_3">
     <property name="geometry">
      <rect>
       <x>30</x>
       <y>130</y>
       <width>61</width>
       <height>23</height>
      </rect>
     </property>
     <property name="accessibleName">
      <string>itemname</string>
     </property>
     <property name="text">
      <string>频率：</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="lblCpuTemperature">
     <property name="geometry">
      <rect>
       <x>101</x>
       <y>70</y>
       <width>211</width>
       <height>23</height>
      </rect>
     </property>
     <property name="text">
      <string>暂无数据</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="lblCpuFrequency">
     <property name="geometry">
      <rect>
       <x>101</x>
       <y>130</y>
       <width>211</width>
       <height>23</height>
      </rect>
     </property>
     <property name="text">
      <string>暂无数据</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="lblCpuStrategy">
     <property name="geometry">
      <rect>
       <x>100</x>
       <y>200</y>
       <width>211</width>
       <height>23</height>
      </rect>
     </property>
     <property name="text">
      <string>暂无数据</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_7">
     <property name="geometry">
      <rect>
       <x>19</x>
       <y>200</y>
       <width>71</width>
       <height>23</height>
      </rect>
     </property>
     <property name="accessibleName">
      <string>itemname</string>
     </property>
     <property name="text">
      <string>策略：</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
    </widget>
    <widget class="QPushButton" name="btnStrategyPerformance">
     <property name="geometry">
      <rect>
       <x>50</x>
       <y>260</y>
       <width>81</width>
       <height>32</height>
      </rect>
     </property>
     <property name="cursor">
      <cursorShape>PointingHandCursor</cursorShape>
     </property>
     <property name="text">
      <string>性能</string>
     </property>
    </widget>
    <widget class="QPushButton" name="btnStrategyOndemand">
     <property name="geometry">
      <rect>
       <x>140</x>
       <y>260</y>
       <width>81</width>
       <height>32</height>
      </rect>
     </property>
     <property name="cursor">
      <cursorShape>PointingHandCursor</cursorShape>
     </property>
     <property name="text">
      <string>按需</string>
     </property>
    </widget>
    <widget class="QPushButton" name="btnStrategyPowersave">
     <property name="geometry">
      <rect>
       <x>230</x>
       <y>260</y>
       <width>81</width>
       <height>32</height>
      </rect>
     </property>
     <property name="cursor">
      <cursorShape>PointingHandCursor</cursorShape>
     </property>
     <property name="text">
      <string>节能</string>
     </property>
    </widget>
   </widget>
   <widget class="QGroupBox" name="groupBox_2">
    <property name="geometry">
     <rect>
      <x>430</x>
      <y>60</y>
      <width>271</width>
      <height>331</height>
     </rect>
    </property>
    <property name="title">
     <string>风扇</string>
    </property>
    <widget class="QLabel" name="label_9">
     <property name="geometry">
      <rect>
       <x>30</x>
       <y>90</y>
       <width>61</width>
       <height>23</height>
      </rect>
     </property>
     <property name="accessibleName">
      <string>itemname</string>
     </property>
     <property name="text">
      <string>转速：</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="lblFanSpeed">
     <property name="geometry">
      <rect>
       <x>101</x>
       <y>90</y>
       <width>121</width>
       <height>23</height>
      </rect>
     </property>
     <property name="text">
      <string>暂无数据</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
    </widget>
    <widget class="QPushButton" name="btnFanSpeedAuto">
     <property name="geometry">
      <rect>
       <x>50</x>
       <y>150</y>
       <width>171</width>
       <height>31</height>
      </rect>
     </property>
     <property name="cursor">
      <cursorShape>PointingHandCursor</cursorShape>
     </property>
     <property name="text">
      <string>设为自动</string>
     </property>
    </widget>
    <widget class="QSlider" name="sdrFanSpeed">
     <property name="geometry">
      <rect>
       <x>40</x>
       <y>260</y>
       <width>181</width>
       <height>31</height>
      </rect>
     </property>
     <property name="cursor">
      <cursorShape>PointingHandCursor</cursorShape>
     </property>
     <property name="minimum">
      <number>85</number>
     </property>
     <property name="maximum">
      <number>255</number>
     </property>
     <property name="pageStep">
      <number>0</number>
     </property>
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
    </widget>
    <widget class="QLabel" name="label_10">
     <property name="geometry">
      <rect>
       <x>40</x>
       <y>230</y>
       <width>61</width>
       <height>23</height>
      </rect>
     </property>
     <property name="accessibleName">
      <string>itemname</string>
     </property>
     <property name="text">
      <string>手动调节</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
    </widget>
   </widget>
   <widget class="QLabel" name="label">
    <property name="geometry">
     <rect>
      <x>60</x>
      <y>410</y>
      <width>121</width>
      <height>21</height>
     </rect>
    </property>
    <property name="text">
     <string>Cputroller v1.1</string>
    </property>
    <property name="alignment">
     <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
    </property>
   </widget>
   <widget class="QLabel" name="lblGithub">
    <property name="geometry">
     <rect>
      <x>590</x>
      <y>410</y>
      <width>61</width>
      <height>21</height>
     </rect>
    </property>
    <property name="text">
     <string>&lt;a href=&quot;https://github.com/chenghaopeng/Cputroller&quot;&gt;GitHub&lt;/a&gt;</string>
    </property>
    <property name="alignment">
     <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
    </property>
    <property name="openExternalLinks">
     <bool>true</bool>
    </property>
   </widget>
   <widget class="QLabel" name="lblMainpage">
    <property name="geometry">
     <rect>
      <x>660</x>
      <y>410</y>
      <width>41</width>
      <height>21</height>
     </rect>
    </property>
    <property name="text">
     <string>&lt;a href=&quot;http://chper.cn&quot;&gt;主页&lt;/a&gt;</string>
    </property>
    <property name="alignment">
     <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
    </property>
    <property name="openExternalLinks">
     <bool>true</bool>
    </property>
   </widget>
  </widget>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources/>
 <connections/>
</ui>
