# Augment MCP服务配置指南

## 配置路径和文件名称

根据Augment官方文档 (https://docs.augmentcode.com/setup-augment/mcp)，有以下配置方式：

### 1. **推荐方式：Augment设置面板** ⭐
- **访问路径**：VS Code → Augment面板 → 右上角设置图标 → MCP部分
- **优势**：可视化界面，支持Easy MCP一键集成
- **存储位置**：由Augment扩展自动管理

### 2. **项目级配置文件**
- **文件路径**：`.augment/mcp.json`
- **作用范围**：当前项目
- **优势**：版本控制，团队共享

### 3. **全局配置文件**
- **文件路径**：`~/.config/augment/mcp.json` (Linux/macOS)
- **文件路径**：`%APPDATA%\augment\mcp.json` (Windows)
- **作用范围**：用户全局

## 当前项目配置文件

### 主配置文件：`.augment/mcp.json`
```json
{
  "mcpServers": {
    "filesystem": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-filesystem", "/home/<USER>/soft/Cputroller-1.1"]
    },
    "everything": {
      "command": "npx", 
      "args": ["-y", "@modelcontextprotocol/server-everything"]
    },
    "memory": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-memory"]
    },
    "sequential-thinking": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]
    },
    "github": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-github"],
      "env": {
        "GITHUB_PERSONAL_ACCESS_TOKEN": "${GITHUB_TOKEN}"
      }
    },
    "slack": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-slack"],
      "env": {
        "SLACK_BOT_TOKEN": "${SLACK_BOT_TOKEN}",
        "SLACK_TEAM_ID": "${SLACK_TEAM_ID}"
      }
    },
    "Context7": {
      "command": "npx",
      "args": ["-y", "@upstash/context7-mcp"]
    }
  }
}
```

### 环境变量文件：`.env`
```bash
# GitHub集成
GITHUB_TOKEN=your_github_token_here

# Slack集成  
SLACK_BOT_TOKEN=your_slack_bot_token_here
SLACK_TEAM_ID=your_slack_team_id_here

# 项目路径
PROJECT_PATH=/home/<USER>/soft/Cputroller-1.1
```

## Easy MCP集成 (推荐)

Augment提供一键集成的服务：

### 可用的Easy MCP服务
1. **CircleCI** - 构建日志和测试洞察
2. **MongoDB** - 数据库管理和代码生成
3. **Redis** - 键空间检查和迁移助手

### 使用方法
1. 打开VS Code中的Augment扩展
2. 进入Easy MCP面板
3. 点击所需集成旁的"+"按钮
4. 粘贴API令牌或OAuth授权
5. 立即开始使用

## 配置优先级

1. **Easy MCP** (最高优先级)
2. **Augment设置面板配置**
3. **项目级配置** (`.augment/mcp.json`)
4. **全局配置** (`~/.config/augment/mcp.json`)
5. **兼容性配置** (`.mcp.json`)

## 最佳实践

### 推荐配置流程
1. 首先尝试使用Easy MCP进行一键集成
2. 对于自定义需求，使用Augment设置面板
3. 对于团队共享，使用项目级配置文件
4. 将敏感信息存储在`.env`文件中

### 安全建议
- 不要在配置文件中硬编码API密钥
- 使用环境变量引用：`"${VARIABLE_NAME}"`
- 将`.env`文件添加到`.gitignore`

## 验证配置

运行验证脚本：
```bash
./validate-mcp.sh
```

或在Augment设置面板中测试连接。

## 故障排除

### 常见问题
1. **服务器未启动**：检查依赖是否安装
2. **环境变量未加载**：确认`.env`文件存在且格式正确
3. **权限问题**：确保有执行MCP服务器的权限

### 调试步骤
1. 检查Augment扩展日志
2. 验证MCP服务器可独立运行
3. 确认网络连接和防火墙设置

---

**配置完成后，重启VS Code以应用新设置。**
