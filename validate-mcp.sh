#!/bin/bash

# MCP配置验证脚本
echo "🔍 正在验证MCP配置..."

# 检查必需的工具
echo "📦 检查工具..."
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装"
    exit 1
fi

if ! command -v npx &> /dev/null; then
    echo "❌ npx 未安装"
    exit 1
fi

echo "✅ Node.js: $(node --version)"
echo "✅ npx: $(npx --version)"

# 检查MCP配置文件
echo "📁 检查配置文件..."
if [ -f ".mcp.json" ]; then
    echo "✅ 项目MCP配置已找到: .mcp.json"
else
    echo "❌ 项目MCP配置未找到"
fi

# 检查环境变量文件
if [ -f ".env.example" ]; then
    echo "✅ 环境变量模板已找到: .env.example"
fi

if [ -f ".env" ]; then
    echo "✅ 环境变量文件已找到: .env"
else
    echo "⚠️  建议创建 .env 文件: cp .env.example .env"
fi

# 检查全局安装的MCP服务器
echo "🔧 检查MCP服务器..."
servers=("@modelcontextprotocol/server-filesystem" "@modelcontextprotocol/server-everything" "@modelcontextprotocol/server-memory" "@modelcontextprotocol/server-sequential-thinking")

for server in "${servers[@]}"; do
    if npm list -g "$server" &> /dev/null; then
        echo "✅ $server 已安装"
    else
        echo "⚠️  $server 未安装"
    fi
done

echo ""
echo "🎯 MCP配置验证完成！"
echo "📖 使用方法:"
echo "   1. 如需自定义环境变量: cp .env.example .env"
echo "   2. 如缺少服务器: npm install -g [server-name]"
echo "   3. 重启您的编辑器以应用新配置"